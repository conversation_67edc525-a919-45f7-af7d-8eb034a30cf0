module "droplet" {
  source = "github.com/aaronromeo/morph//modules/dokku"

  providers = {
    digitalocean = digitalocean
  }

  do_api_token = var.do_api_token
  
  environment      = "production"
  droplet_size     = "s-1vcpu-1gb"
  region           = var.region
  SSH_FINGERPRINTS = var.SSH_FINGERPRINTS
  dokku_hostname   = "overachieverlabs.com"
  PUBLIC_KEY_SHA   = var.PUBLIC_KEY_SHA
  PUBLIC_KEY_FILE  = var.PUBLIC_KEY_FILE
}

# Production-specific resources
# First, try to get the domain if it exists
data "digitalocean_domain" "existing_domain" {
  count = var.check_domain_exists ? 1 : 0
  name  = var.DOMAIN
}

# Create the domain if it doesn't exist or if we're not checking
resource "digitalocean_domain" "app_domain" {
  # Only create if we're not checking for existing domains or if the data lookup failed
  count = var.check_domain_exists ? 0 : 1
  name  = var.DOMAIN
}

# Use either the existing domain or the newly created one
locals {
  domain_name = var.check_domain_exists ? (length(data.digitalocean_domain.existing_domain) > 0 ? data.digitalocean_domain.existing_domain[0].name : var.DOMAIN) : digitalocean_domain.app_domain[0].name
}

# Add an A record for the root domain pointing to the Dokku droplet
resource "digitalocean_record" "root_domain" {
  domain = local.domain_name
  type   = "A"
  name   = "@"  # @ represents the root domain
  ttl    = 1800
  value  = module.droplet.droplet_ip
}

# Add a wildcard A record for all subdomains pointing to the Dokku droplet
resource "digitalocean_record" "wildcard_domain" {
  domain = local.domain_name
  type   = "A"
  name   = "*"  # * represents all subdomains
  ttl    = 1800
  value  = module.droplet.droplet_ip
}
